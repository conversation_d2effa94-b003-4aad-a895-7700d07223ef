"""
Move To Module
Konversi koordinat pixel ke posisi Grbl untuk fitur Move To
Menggunakan sistem Grbl yang sudah ada di Software/Grbl/Grbl.py
"""

from typing import Tuple

class MoveToController:
    def __init__(self,
                 center_pixel_x,
                 center_pixel_y,
                 center_grbl_x,
                 center_grbl_y,
                 pixel_to_mm_ratio,
                 grbl_instance=None):
        """
        Inisialisasi MoveToController

        Args:
            center_pixel_x: Koordinat X pixel tengah dari resolusi penuh 1920x1080 (default: 960)
            center_pixel_y: Koordinat Y pixel tengah dari resolusi penuh 1920x1080 (default: 540)
            center_grbl_x: Posisi X Grbl untuk titik tengah pixel (default: 20.0)
            center_grbl_y: Posisi Y Grbl untuk titik tengah pixel (default: 20.0)
            pixel_to_mm_ratio: Rasio konversi pixel ke mm (default: 0.1 mm per pixel)
            grbl_instance: Instance dari class Grbl yang sudah ada (opsional)
        """
        self.center_pixel_x = center_pixel_x
        self.center_pixel_y = center_pixel_y
        self.center_grbl_x = center_grbl_x
        self.center_grbl_y = center_grbl_y
        self.pixel_to_mm_ratio = pixel_to_mm_ratio
        self.grbl_instance = grbl_instance

    def set_grbl_instance(self, grbl_instance):
        """
        Set instance Grbl yang akan digunakan untuk komunikasi

        Args:
            grbl_instance: Instance dari class Grbl
        """
        self.grbl_instance = grbl_instance
    
    def pixel_to_grbl_coordinates(self, pixel_x: int, pixel_y: int) -> Tuple[float, float]:
        """
        Konversi koordinat pixel ke koordinat Grbl

        PENTING: Koordinat pixel yang digunakan adalah dari gambar asli resolusi penuh (1920x1080),
        bukan dari gambar yang sudah di-crop. Fitur crop hanya untuk tampilan yang lebih jelas,
        tetapi koordinat Move To tetap menggunakan pixel original.

        Args:
            pixel_x: Koordinat X dalam pixel dari gambar asli (0-1920)
            pixel_y: Koordinat Y dalam pixel dari gambar asli (0-1080)

        Returns:
            Tuple[float, float]: Koordinat Grbl (X, Y)
        """
        # Hitung perbedaan dari titik tengah resolusi penuh (960, 540)
        delta_x_pixel = pixel_x - self.center_pixel_x
        delta_y_pixel = pixel_y - self.center_pixel_y

        # Konversi perbedaan pixel ke mm
        delta_x_mm = delta_x_pixel / self.pixel_to_mm_ratio
        delta_y_mm = delta_y_pixel / self.pixel_to_mm_ratio

        # Hitung posisi Grbl final
        grbl_x = 27.957 + self.center_grbl_x + delta_x_mm
        grbl_y = 56.754 - self.center_grbl_y - delta_y_mm

        print(f'delta_x {delta_x_pixel} = {pixel_x} - {self.center_pixel_x}')
        print(f'delta_y {delta_y_pixel} = {pixel_y} - {self.center_pixel_y}' )

        print(f'grbl_x {grbl_x} = 27.957 + {self.center_grbl_x} + {delta_x_mm}')
        print(f'grbl_y {grbl_y} = 56.754 - {self.center_grbl_y} + {delta_y_mm}')
        return grbl_x, grbl_y
    
    def send_grbl_command(self, command: str) -> bool:
        """
        Mengirim command ke Grbl menggunakan instance yang sudah ada

        Args:
            command: Command Grbl yang akan dikirim

        Returns:
            bool: True jika berhasil dikirim, False jika gagal
        """
        if self.grbl_instance is None:
            print("Move To: Instance Grbl tidak tersedia")
            return False

        try:
            # Gunakan method send_command dari instance Grbl yang sudah ada
            self.grbl_instance.send_command(command)
            print(f"Move To: Command dikirim melalui Grbl instance: {command}")
            return True
        except Exception as e:
            print(f"Move To: Error mengirim command: {e}")
            return False
    
    def move_to_pixel(self, pixel_x: int, pixel_y: int, feed_rate: int = 1000) -> bool:
        """
        Bergerak ke posisi berdasarkan koordinat pixel dari gambar asli (resolusi penuh)

        Args:
            pixel_x: Koordinat X dalam pixel dari gambar asli (0-1920)
            pixel_y: Koordinat Y dalam pixel dari gambar asli (0-1080)
            feed_rate: Kecepatan gerakan (mm/min)

        Returns:
            bool: True jika berhasil, False jika gagal
        """
        # Validasi koordinat pixel dalam range yang valid
        if not (0 <= pixel_x <= 1920) or not (0 <= pixel_y <= 1080):
            print(f"Move To Warning: Koordinat pixel ({pixel_x}, {pixel_y}) di luar range resolusi 1920x1080")

        # Konversi pixel ke koordinat Grbl
        grbl_x, grbl_y = self.pixel_to_grbl_coordinates(pixel_x, pixel_y)

        print(f"Move To: Konversi pixel original ({pixel_x}, {pixel_y}) ke Grbl ({grbl_x:.3f}, {grbl_y:.3f})")

        # Buat G-code command
        gcode_command = f"G0 X{grbl_x:.3f} Y{grbl_y:.3f} F{feed_rate}"

        # Kirim command ke Grbl
        success = self.send_grbl_command(gcode_command)

        if success:
            print(f"Move To: Berhasil mengirim command untuk bergerak ke posisi ({grbl_x:.3f}, {grbl_y:.3f})")
            return True
        else:
            print(f"Move To: Gagal mengirim command untuk bergerak ke posisi ({grbl_x:.3f}, {grbl_y:.3f})")
            return False
    
    def move_to_grbl_position(self, grbl_x: float, grbl_y: float, feed_rate: int = 1000) -> bool:
        """
        Bergerak ke posisi Grbl langsung

        Args:
            grbl_x: Koordinat X Grbl
            grbl_y: Koordinat Y Grbl
            feed_rate: Kecepatan gerakan (mm/min)

        Returns:
            bool: True jika berhasil, False jika gagal
        """
        # Buat G-code command
        gcode_command = f"G0 X{grbl_x:.3f} Y{grbl_y:.3f} F{feed_rate}"

        # Kirim command ke Grbl
        success = self.send_grbl_command(gcode_command)

        if success:
            print(f"Move To: Berhasil mengirim command untuk bergerak ke posisi ({grbl_x:.3f}, {grbl_y:.3f})")
            return True
        else:
            print(f"Move To: Gagal mengirim command untuk bergerak ke posisi ({grbl_x:.3f}, {grbl_y:.3f})")
            return False

    def get_current_position(self) -> Tuple[float, float, float]:
        """
        Mendapatkan posisi saat ini dari Grbl menggunakan instance yang sudah ada

        Returns:
            Tuple[float, float, float]: Posisi saat ini (X, Y, Z)
        """
        if self.grbl_instance is None:
            print("Move To: Instance Grbl tidak tersedia")
            return 0.0, 0.0, 0.0

        try:
            # Gunakan method get_current_position dari instance Grbl yang sudah ada
            return self.grbl_instance.get_current_position()
        except Exception as e:
            print(f"Move To: Error mendapatkan posisi: {e}")
            return 0.0, 0.0, 0.0

