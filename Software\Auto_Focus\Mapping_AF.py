import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QThread, pyqtSlot
import time

class MappingAFWorker(QObject):
    finished = pyqtSignal()
    progress = pyqtSignal(int, str)
    log_message = pyqtSignal(str)

    def __init__(self, camera, grbl, grbl_start, grbl_end, stitching_offset=0.5):
        super().__init__()
        self.camera = camera
        self.grbl = grbl
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.stitching_offset = stitching_offset # Renamed for clarity: this is the resolution for the full stitching grid
        self.is_running = True
        self.results = {}

        # Import here to avoid circular import
        from .AutoFocus_Worker import AutoFocusWorker

        # Create dedicated autofocus worker for mapping
        self.autofocus_worker = AutoFocusWorker(camera, grbl)

        # State management
        self.current_point = 0
        self.total_points = 9
        self.points_queue = []
        self.current_af_type = None  # 'full' or 'refine'

        # Movement and process flags
        self.is_moving = False
        self.is_autofocus_running = False
        self.base_movement_timeout = 30.0  # base timeout in seconds
        self.polling_was_active = False  # Track if polling was active before

        # Connect autofocus signals
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished)

    def _pre_run_logging(self, x_points, y_points):
        """Generates a log of calculated grid points before execution."""
        log = "\n" + "="*50 + "\n"
        log += " " * 10 + "MAPPING AF PRE-CALCULATION LOG\n"
        log += "="*50 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += "EFFICIENT Processing Order: 5→4→7→8→9→6→3→2→1\n"
        log += "Grid Layout: 1 2 3\n"
        log += "             4 5 6\n"
        log += "             7 8 9\n"
        log += "-"*50 + "\n"

        # Define the efficient order for logging
        efficient_order = [
            (1, 1, 'full', 5),   # Center
            (1, 0, 'refine', 4), # Left middle
            (2, 0, 'refine', 7), # Bottom left
            (2, 1, 'refine', 8), # Bottom center
            (2, 2, 'refine', 9), # Bottom right
            (1, 2, 'refine', 6), # Right middle
            (0, 2, 'refine', 3), # Top right
            (0, 1, 'refine', 2), # Top center
            (0, 0, 'refine', 1)  # Top left
        ]

        # Show points in processing order
        for order_num, (i, j, af_type, grid_num) in enumerate(efficient_order, 1):
            x = x_points[j]
            y = y_points[i]
            af_desc = "Full AF - Baseline" if af_type == 'full' else "Refine Scan"
            log += f"  {order_num}. Grid {grid_num}: X={x:.3f}, Y={y:.3f}  [{af_desc}]\n"

        log += "="*50 + "\n"
        self.log_message.emit(log)

    def _generate_stitching_grid_coords(self, x_start, y_start, x_end, y_end, stitching_offset):
        """
        Generates the full grid coordinates for stitching based on the original ROI and stitching_offset.
        This offset defines the step size between points.
        """
        x_range = x_end - x_start
        y_range = y_end - y_start # Keep original sign for y_range

        if stitching_offset <= 0:
            # Fallback to a minimal grid if offset is invalid
            return np.array([x_start, (x_start + x_end) / 2, x_end]), \
                   np.array([y_start, (y_start + y_end) / 2, y_end])

        x_points_count = int(round(x_range / stitching_offset)) + 1
        y_points_count = int(round(abs(y_range) / stitching_offset)) + 1 # Use abs for count, but linspace handles direction

        # Ensure at least 3 points for a basic grid, even if range is small
        x_points_count = max(3, x_points_count)
        y_points_count = max(3, y_points_count)

        x_stitching_coords = np.linspace(x_start, x_end, x_points_count)
        y_stitching_coords = np.linspace(y_start, y_end, y_points_count)
        
        return x_stitching_coords, y_stitching_coords

    def _select_af_points_from_stitching_grid(self, x_stitching_coords, y_stitching_coords):
        """
        Selects 9 AF points (3x3 conceptual grid) from the larger stitching grid.
        Points are chosen at approximately 25%, 50%, 75% of the range to avoid edges.
        Handles cases where the stitching grid is too small.
        """
        # Ensure we have at least 3 unique points for AF in each dimension
        # If not, duplicate existing points to form a 3-point conceptual grid
        
        # For X coordinates
        if len(x_stitching_coords) < 3:
            if len(x_stitching_coords) == 1:
                af_x_indices = [0, 0, 0] # Use the single point three times
            else: # len == 2
                af_x_indices = [0, 0, 1] # Use first point twice, second once
        else:
            # Select indices for 25%, 50%, 75% of the range
            idx_25 = int(round(0.25 * (len(x_stitching_coords) - 1)))
            idx_50 = int(round(0.50 * (len(x_stitching_coords) - 1)))
            idx_75 = int(round(0.75 * (len(x_stitching_coords) - 1)))
            af_x_indices = [idx_25, idx_50, idx_75]
        
        # For Y coordinates
        if len(y_stitching_coords) < 3:
            if len(y_stitching_coords) == 1:
                af_y_indices = [0, 0, 0] # Use the single point three times
            else: # len == 2
                af_y_indices = [0, 0, 1] # Use first point twice, second once
        else:
            # Select indices for 25%, 50%, 75% of the range
            idx_25 = int(round(0.25 * (len(y_stitching_coords) - 1)))
            idx_50 = int(round(0.50 * (len(y_stitching_coords) - 1)))
            idx_75 = int(round(0.75 * (len(y_stitching_coords) - 1)))
            af_y_indices = [idx_25, idx_50, idx_75]

        # Map indices to actual coordinates
        x_af_points = np.array([x_stitching_coords[i] for i in af_x_indices])
        y_af_points = np.array([y_stitching_coords[i] for i in af_y_indices])

        return x_af_points, y_af_points

    def run(self):
        """
        Main run method - sets up the points queue and starts processing
        """
        try:
            self.log_message.emit(f"MappingAFWorker thread: {QThread.currentThreadId()}")

            # Check if GRBL polling is active and disable it during mapping
            self.polling_was_active = hasattr(self.grbl, 'polling_timer') and self.grbl.polling_timer.isActive()
            if self.polling_was_active:
                self.log_message.emit("[MAPPING] Disabling GRBL polling for stable operation")
                self.grbl.stop_polling()
            else:
                self.log_message.emit("[MAPPING] GRBL polling was not active")

            x_start, y_start = self.grbl_start
            x_end, y_end = self.grbl_end

            # Generate the new 3x3 grid points based on the offset
            # First, generate the full stitching grid coordinates
            x_stitching_coords, y_stitching_coords = self._generate_stitching_grid_coords(
                x_start, y_start, x_end, y_end, self.stitching_offset
            )
            self.log_message.emit(f"Generated stitching grid: {len(x_stitching_coords)}x{len(y_stitching_coords)} using offset {self.stitching_offset}")

            # Then, select the 9 AF points from this stitching grid
            x_af_points, y_af_points = self._select_af_points_from_stitching_grid(
                x_stitching_coords, y_stitching_coords
            )
            self.log_message.emit(f"Selected 9 AF points from stitching grid.")

            # Emit the pre-calculation log for the selected AF points
            self._pre_run_logging(x_af_points, y_af_points)
            self.points_queue = []

            # Define the efficient order mapping (conceptual grid position -> order)
            # This order is fixed for a 3x3 conceptual grid
            efficient_order = [
                (1, 1, 'full'),   # Center (full AF first)
                (1, 0, 'refine'), # Left middle
                (2, 0, 'refine'), # Bottom left
                (2, 1, 'refine'), # Bottom center
                (2, 2, 'refine'), # Bottom right
                (1, 2, 'refine'), # Right middle
                (0, 2, 'refine'), # Top right
                (0, 1, 'refine'), # Top center
                (0, 0, 'refine')  # Top left
            ]

            # Add the selected AF points to the queue in the efficient order
            for i, j, af_type in efficient_order:
                x = x_af_points[j] # x_af_points is 1D array of 3 x-coords
                y = y_af_points[i] # y_af_points is 1D array of 3 y-coords
                self.points_queue.append((x, y, af_type))

                grid_num = i * 3 + j + 1 # Calculate conceptual grid number (1-9)
                af_desc = "Full AF - Baseline" if af_type == 'full' else "Refine Scan"
                self.log_message.emit(f"Point {grid_num} added: ({x:.3f}, {y:.3f}) - {af_desc}")

            self.log_message.emit(f"Total points in queue: {len(self.points_queue)} (1 full AF + {len(self.points_queue)-1} refine AF)")

            self.current_point = 0
            self.total_points = len(self.points_queue)

            # Start processing first point
            self.process_next_point()

        except Exception as e:
            self.log_message.emit(f"Error during Mapping AF initialization: {e}")
            self.progress.emit(100, f"Error during Mapping AF: {e}")
            self.finished.emit()

    def process_next_point(self):
        """
        Process the next point in the queue
        """
        if not self.is_running or self.current_point >= len(self.points_queue):
            # All points processed
            self.log_results()
            self.progress.emit(100, "Mapping AF finished.")

            # Re-enable GRBL polling only if it was active before
            if self.polling_was_active:
                self.log_message.emit("[MAPPING] Re-enabling GRBL polling")
                if hasattr(self.grbl, 'start_polling'):
                    self.grbl.start_polling()
                    self.log_message.emit("[MAPPING] ✓ GRBL polling restarted successfully")
            else:
                self.log_message.emit("[MAPPING] GRBL polling was not active, not restarting")

            # Update final position
            try:
                final_x, final_y, final_z = self.grbl.get_current_position()
                self.log_message.emit(f"[MAPPING] Final position: X={final_x:.3f}, Y={final_y:.3f}, Z={final_z:.3f}")
            except Exception as e:
                self.log_message.emit(f"[MAPPING] Error getting final position: {e}")

            self.finished.emit()
            return

        try:
            x, y, af_type = self.points_queue[self.current_point]
            self.current_af_type = af_type

            progress_percent = int((self.current_point / self.total_points) * 100)
            point_description = "CENTER (Full AF)" if af_type == 'full' else f"Point {self.current_point + 1}/{self.total_points} (Refine)"
            self.progress.emit(progress_percent, f"{point_description} at ({x:.2f}, {y:.2f})")

            # Start movement to position
            self.log_message.emit(f"Moving to position ({x:.2f}, {y:.2f})")
            self.is_moving = True
            self.grbl.move_to(x, y)

            # Wait for movement completion with robust checking
            if self._wait_for_movement_complete(x, y):
                self.log_message.emit(f"✓ Movement complete to ({x:.2f}, {y:.2f})")

                # Start autofocus only after movement is confirmed complete
                self.is_autofocus_running = True
                if af_type == 'full':
                    self.log_message.emit(f"Starting FULL autofocus at CENTER ({x:.2f}, {y:.2f}) - Establishing baseline Z")
                    self.autofocus_worker.run_autofocus()
                else:
                    self.log_message.emit(f"Starting REFINE autofocus at ({x:.2f}, {y:.2f}) - Using baseline Z")
                    self.autofocus_worker._refine_mode = True # Explicitly set refine mode
                    self.autofocus_worker.run_refinement_autofocus()
            else:
                self.log_message.emit(f"❌ Movement FAILED to ({x:.2f}, {y:.2f}) - Skipping this point")
                # Skip this point and move to next
                self.current_point += 1
                self.process_next_point()

        except Exception as e:
            self.log_message.emit(f"Error processing point {self.current_point}: {e}")
            self.current_point += 1
            self.process_next_point()

    @pyqtSlot(float, float)
    def on_autofocus_finished(self, best_pos, best_score):
        """
        Called when autofocus finishes for current point
        """
        try:
            self.is_autofocus_running = False
            self.log_message.emit(f"[MAPPING] AF finished signal received: Z={best_pos:.4f}, Score={best_score:.2f}")

            if self.current_point < len(self.points_queue):
                x, y, af_type = self.points_queue[self.current_point]

                # Validate result
                if best_pos <= 0:
                    self.log_message.emit(f"⚠️ WARNING: Invalid Z position {best_pos:.4f} at ({x:.2f}, {y:.2f}) - AF may have failed")
                    self.results[(x, y)] = 0.0  # Store as failed
                else:
                    self.results[(x, y)] = best_pos

                if af_type == 'full':
                    if best_pos > 0:
                        self.log_message.emit(f"✓ CENTER AF finished: Z={best_pos:.4f}, Score={best_score:.2f} - BASELINE ESTABLISHED")
                    else:
                        self.log_message.emit(f"❌ CENTER AF FAILED: Z={best_pos:.4f}, Score={best_score:.2f}")
                else:
                    if best_pos > 0:
                        self.log_message.emit(f"✓ Refine AF finished at ({x:.2f}, {y:.2f}): Z={best_pos:.4f}, Score={best_score:.2f}")
                    else:
                        self.log_message.emit(f"❌ Refine AF FAILED at ({x:.2f}, {y:.2f}): Z={best_pos:.4f}, Score={best_score:.2f}")

            # Small delay before moving to next point to ensure stability
            import time
            time.sleep(0.5)

            # Move to next point
            self.current_point += 1
            self.process_next_point()

        except Exception as e:
            self.log_message.emit(f"Error in on_autofocus_finished: {e}")
            self.is_autofocus_running = False
            self.current_point += 1
            self.process_next_point()

    def log_results(self):
        log = "\n" + "="*50 + "\n"
        log += " " * 15 + "MAPPING AF RESULTS\n"
        log += "="*50 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"ROI Area (GRBL):\n"
        log += f"  - Start: X={self.grbl_start[0]:.3f}, Y={self.grbl_start[1]:.3f}\n"
        log += f"  - End:   X={self.grbl_end[0]:.3f}, Y={self.grbl_end[1]:.3f}\n"
        log += "-"*50 + "\n"
        # Regenerate the stitching grid coordinates to ensure consistency in logging
        x_stitching_coords, y_stitching_coords = self._generate_stitching_grid_coords(
            self.grbl_start[0], self.grbl_start[1],
            self.grbl_end[0], self.grbl_end[1],
            self.stitching_offset
        )
        # Select the AF points from this regenerated stitching grid for logging
        x_af_points, y_af_points = self._select_af_points_from_stitching_grid(
            x_stitching_coords, y_stitching_coords
        )

        log += "Selected 3x3 AF Points (Z-position):\n"

        # Display AF points in conceptual 3x3 grid format
        for i in range(3):
            row_str = ""
            for j in range(3):
                x = x_af_points[j]
                y = y_af_points[i]
                z_val = self.results.get((x, y), float('nan'))
                grid_num = i * 3 + j + 1
                row_str += f"  {grid_num}:({x:.3f},{y:.3f})->Z={z_val:.4f} |"
            log += row_str[:-2] + "\n"

        log += "\nDetailed AF Point List:\n"
        for i in range(3):
            for j in range(3):
                x = x_af_points[j]
                y = y_af_points[i]
                z_val = self.results.get((x, y), float('nan'))
                grid_num = i * 3 + j + 1
                log += f"  AF Point {grid_num}: X={x:.3f}, Y={y:.3f}, Z={z_val:.4f}\n"

        log += f"\nTotal mapped points: {len(self.results)}\n"
        log += "="*50 + "\n"
        log += "NOTE: Use Z_Interpolation class to generate interpolated grid\n"
        log += "="*50 + "\n"

        self.log_message.emit(log)

    def _calculate_movement_timeout(self, target_x, target_y):
        """
        Calculate dynamic timeout based on distance and feedrate
        """
        try:
            # Get current position
            current_x, current_y, _ = self.grbl.get_current_position()

            # Calculate distance
            distance = ((target_x - current_x)**2 + (target_y - current_y)**2)**0.5

            # Get feedrate (use default for now, can be improved later)
            feedrate = 1000  # mm/min - default feedrate
            self.log_message.emit(f"[MOVEMENT] Using default feedrate: {feedrate} mm/min")

            # Calculate time needed: distance(mm) / feedrate(mm/min) * 60(s/min)
            estimated_time = (distance / feedrate) * 60

            # Add safety margin (3x estimated time, minimum 30s, maximum 120s)
            timeout = max(30, min(120, estimated_time * 3))

            self.log_message.emit(f"[MOVEMENT] Distance: {distance:.2f}mm, Feedrate: {feedrate}mm/min, Timeout: {timeout:.1f}s")
            return timeout

        except Exception as e:
            self.log_message.emit(f"[MOVEMENT] Error calculating timeout: {e}, using default")
            return self.base_movement_timeout

    def _wait_for_movement_complete(self, target_x=None, target_y=None):
        """
        Wait for GRBL movement to complete with robust checking
        Returns True if movement completed successfully, False if timeout or error
        """
        import time

        # Calculate dynamic timeout if target position provided
        if target_x is not None and target_y is not None:
            movement_timeout = self._calculate_movement_timeout(target_x, target_y)
        else:
            movement_timeout = self.base_movement_timeout

        start_time = time.time()
        consecutive_idle_count = 0
        required_idle_count = 3  # Require 3 consecutive 'Idle' status to confirm completion

        self.log_message.emit(f"[MOVEMENT] Waiting for movement completion (timeout: {movement_timeout:.1f}s)")

        while time.time() - start_time < movement_timeout:
            if not self.is_running:
                self.log_message.emit("[MOVEMENT] Stopped by user")
                return False

            try:
                # Get current status
                status = self.grbl.get_status()
                self.log_message.emit(f"[MOVEMENT] Status: {status}")

                if status == 'Idle':
                    consecutive_idle_count += 1
                    if consecutive_idle_count >= required_idle_count:
                        # Additional stabilization delay
                        time.sleep(0.5)
                        self.is_moving = False
                        self.log_message.emit(f"[MOVEMENT] ✓ Movement confirmed complete after {consecutive_idle_count} consecutive Idle checks")
                        return True
                else:
                    consecutive_idle_count = 0  # Reset counter if not idle

                # Small delay between checks
                time.sleep(0.2)

            except Exception as e:
                self.log_message.emit(f"[MOVEMENT] Error checking status: {e}")
                time.sleep(0.5)

        # Timeout reached
        self.is_moving = False
        self.log_message.emit(f"[MOVEMENT] ❌ TIMEOUT after {movement_timeout:.1f}s")
        return False

    def stop(self):
        """
        Stop the mapping AF process
        """
        self.is_running = False
        self.is_moving = False
        self.is_autofocus_running = False

        if self.autofocus_worker and self.autofocus_worker.is_running:
            self.autofocus_worker.stop_autofocus()
        self.log_message.emit("Mapping AF stopped by user")