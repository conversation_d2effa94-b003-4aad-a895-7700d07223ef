import numpy as np
import time
from scipy.interpolate import griddata, RectBivariateSpline

class ZInterpolation:
    """
    Class for interpolating Z values from a 3x3 grid to a higher resolution grid
    using bilinear interpolation for efficient auto-focus mapping.
    """

    def __init__(self, grbl_start, grbl_end, mapping_results, stitching_offset=0.5):
        """
        Initialize Z interpolation with mapping results from a 3x3 conceptual grid.

        Args:
            grbl_start: (x_start, y_start) tuple of the original ROI.
            grbl_end: (x_end, y_end) tuple of the original ROI.
            mapping_results: Dictionary with (x,y) keys and z values from the conceptual 3x3 AF points.
            stitching_offset: Grid offset for interpolation (default 0.5). This defines the step size for the full stitching grid.
        """
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.mapping_results = mapping_results
        self.stitching_offset = stitching_offset

        # Generate the full stitching grid coordinates based on grbl_start, grbl_end, and stitching_offset
        x_range = grbl_end[0] - grbl_start[0]
        y_range = grbl_end[1] - grbl_start[1] # Keep original sign for y_range

        if stitching_offset <= 0:
            # Fallback to a minimal grid if offset is invalid
            self.x_points_count = 3
            self.y_points_count = 3
            self.x_interpolated = np.linspace(grbl_start[0], grbl_end[0], self.x_points_count)
            self.y_interpolated = np.linspace(grbl_start[1], grbl_end[1], self.y_points_count)
        else:
            self.x_points_count = int(round(x_range / stitching_offset)) + 1
            self.y_points_count = int(round(abs(y_range) / stitching_offset)) + 1 # Use abs for count, but linspace handles direction

            # Ensure at least 3 points for a basic grid, even if range is small
            self.x_points_count = max(3, self.x_points_count)
            self.y_points_count = max(3, self.y_points_count)

            self.x_interpolated = np.linspace(grbl_start[0], grbl_end[0], self.x_points_count)
            self.y_interpolated = np.linspace(grbl_start[1], grbl_end[1], self.y_points_count)

        # The original points for interpolation are the keys from the mapping results
        # These are the 9 AF points selected from the stitching grid
        x_af_coords = sorted(list(set(k[0] for k in mapping_results.keys())))
        y_af_coords = sorted(list(set(k[1] for k in mapping_results.keys())))
        self.x_original_af = np.array(x_af_coords)
        self.y_original_af = np.array(y_af_coords)

        # Store interpolated results for all methods
        self.interpolated_results = {
            'bilinear': {},
            'bicubic': {},
            'spline': {}
        }
        self.interpolated_z_grid = {
            'bilinear': None,
            'bicubic': None,
            'spline': None
        }

        print(f"Grid configuration:")
        print(f"  AF Points (conceptual 3x3): {len(self.x_original_af)}x{len(self.y_original_af)} unique points from mapping results")
        print(f"  Stitching Grid: {self.x_points_count}x{self.y_points_count} grid")
        print(f"  Stitching Offset: {stitching_offset}")
        print(f"  Total stitching points: {self.x_points_count * self.y_points_count}")

    def validate_mapping_data(self):
        """
        Validate that we have all 9 points from the mapping

        Returns:
            bool: True if all points are valid, False otherwise
        """
        expected_points = []
        # We expect 9 points, but they might not be unique if the stitching grid was small
        # So we just check if the mapping_results has enough data
        if len(self.mapping_results) < 3: # At least 3 unique points needed for interpolation
            print(f"X Insufficient unique AF points for interpolation: {len(mapping_results)}")
            return False

        missing_points = []
        invalid_z_points = []

        for point in expected_points:
            if point not in self.mapping_results:
                missing_points.append(point)
            elif self.mapping_results[point] <= 0:
                invalid_z_points.append(point)

        if missing_points:
            print(f"X Missing mapping points: {missing_points}")
            return False

        if invalid_z_points:
            print(f"! Invalid Z values (≤0) at points: {invalid_z_points}")
            # Replace invalid Z values with nearest valid neighbor
            self._fix_invalid_z_values(invalid_z_points)

        return True

    def _fix_invalid_z_values(self, invalid_points):
        """
        Replace invalid Z values (≤0) with nearest valid neighbor values

        Args:
            invalid_points: List of (x,y) tuples with invalid Z values
        """
        print(f"[Z_INTERPOLATION] Fixing {len(invalid_points)} invalid Z values...")

        for invalid_point in invalid_points:
            # Find nearest valid point
            min_distance = float('inf')
            nearest_z = None

            for point, z_value in self.mapping_results.items():
                if z_value > 0 and point != invalid_point:
                    distance = ((point[0] - invalid_point[0])**2 + (point[1] - invalid_point[1])**2)**0.5
                    if distance < min_distance:
                        min_distance = distance
                        nearest_z = z_value

            if nearest_z is not None:
                old_z = self.mapping_results[invalid_point]
                self.mapping_results[invalid_point] = nearest_z
                print(f"  Fixed point {invalid_point}: Z={old_z:.4f} -> Z={nearest_z:.4f} (distance: {min_distance:.3f})")
            else:
                print(f"  WARNING: No valid neighbor found for point {invalid_point}")

    def _bilinear_interpolate(self, x, y):
        """
        Perform bilinear interpolation for a single point using the 3x3 grid.
        """
        # Find the grid cell containing the point
        x_idx = -1
        y_idx = -1

        if len(self.x_original_af) < 2 or len(self.y_original_af) < 2:
             return 0 # Not enough unique AF points to interpolate

        for i in range(len(self.x_original_af) - 1):
            if x >= self.x_original_af[i] and x <= self.x_original_af[i+1]:
                x_idx = i
                break
        if x_idx == -1: x_idx = 0 # Handle edge case

        for i in range(len(self.y_original_af) - 1):
            if y >= self.y_original_af[i] and y <= self.y_original_af[i+1]:
                y_idx = i
                break
        if y_idx == -1: y_idx = 0 # Handle edge case

        # Get the four corner points from the AF points
        x1, x2 = self.x_original_af[x_idx], self.x_original_af[x_idx+1]
        y1, y2 = self.y_original_af[y_idx], self.y_original_af[y_idx+1]

        # Get Z values at corners
        z11 = self.mapping_results.get((x1, y1), 0)
        z12 = self.mapping_results.get((x1, y2), 0)
        z21 = self.mapping_results.get((x2, y1), 0)
        z22 = self.mapping_results.get((x2, y2), 0)

        # Fallback to nearest neighbor if a corner has invalid Z
        if any(z <= 0 for z in [z11, z12, z21, z22]):
            distances = []
            for (orig_x, orig_y), z_val in self.mapping_results.items():
                if z_val > 0:
                    dist = ((x - orig_x)**2 + (y - orig_y)**2)**0.5
                    distances.append((dist, z_val))
            if distances:
                return min(distances, key=lambda item: item[0])[1]
            return 0

        # Perform bilinear interpolation
        if x2 == x1 or y2 == y1: return z11 # Avoid division by zero
        
        t_x = (x - x1) / (x2 - x1)
        t_y = (y - y1) / (y2 - y1)

        z_y1 = z11 * (1 - t_x) + z21 * t_x
        z_y2 = z12 * (1 - t_x) + z22 * t_x
        
        return z_y1 * (1 - t_y) + z_y2 * t_y

    def _create_bilinear_grid(self):
        """Calculates the bilinear interpolation grid."""
        print("Creating bilinear interpolated grid...")
        results = {}
        for y in self.y_interpolated:
            for x in self.x_interpolated:
                results[(x, y)] = self._bilinear_interpolate(x, y)
        
        self.interpolated_results['bilinear'] = results
        grid = np.zeros((self.y_points_count, self.x_points_count))
        for i, y in enumerate(self.y_interpolated):
            for j, x in enumerate(self.x_interpolated):
                grid[i, j] = results.get((x, y), 0)
        self.interpolated_z_grid['bilinear'] = grid
        print("V Bilinear interpolation complete.")

    def _create_scipy_grid(self, method):
        """Calculates interpolation using scipy.griddata for methods like 'cubic' or 'linear'."""
        print(f"Creating {method} interpolated grid...")
        
        # Prepare source points and values
        points = np.array(list(self.mapping_results.keys()))
        values = np.array(list(self.mapping_results.values()))
        
        # Prepare target grid
        grid_x, grid_y = np.meshgrid(self.x_interpolated, self.y_interpolated)
        
        # Perform interpolation
        grid_z = griddata(points, values, (grid_x, grid_y), method=method)
        
        # Store results
        results = {}
        for i, y in enumerate(self.y_interpolated):
            for j, x in enumerate(self.x_interpolated):
                results[(x, y)] = grid_z[i, j]
        
        self.interpolated_results[method] = results
        self.interpolated_z_grid[method] = grid_z
        print(f"V {method.capitalize()} interpolation complete.")

    def create_interpolation_grid(self):
        """
        Create interpolated Z values using all available methods.
        """
        try:
            if not self.validate_mapping_data():
                print("X Cannot interpolate: Invalid mapping data")
                return False

            valid_points = sum(1 for z in self.mapping_results.values() if z > 0)
            if valid_points < 4:
                print(f"X Insufficient valid points for interpolation: {valid_points}")
                return False

            # --- Run all interpolation methods ---
            self._create_bilinear_grid()
            self._create_scipy_grid('cubic') # Bicubic
            
            # For spline, we use RectBivariateSpline which requires strictly increasing coordinates
            print("Creating spline interpolated grid...")

            # Ensure coordinates are strictly increasing for spline interpolation (using AF points)
            x_spline = np.sort(self.x_original_af)
            y_spline = np.sort(self.y_original_af)

            # Create Z grid with proper ordering from AF points
            z_original = np.zeros((len(y_spline), len(x_spline)))
            for i, y in enumerate(y_spline):
                for j, x in enumerate(x_spline):
                    z_original[i, j] = self.mapping_results.get((x, y), 0)

            # Create spline interpolator with sorted coordinates
            f_spline = RectBivariateSpline(y_spline, x_spline, z_original, kx=2, ky=2)

            # Interpolate on the target grid (which may have different ordering)
            x_interp_sorted = np.sort(self.x_interpolated)
            y_interp_sorted = np.sort(self.y_interpolated)
            spline_grid_sorted = f_spline(y_interp_sorted, x_interp_sorted)

            # Reorder results to match original interpolation grid ordering
            spline_grid = np.zeros((len(self.y_interpolated), len(self.x_interpolated)))
            for i, y in enumerate(self.y_interpolated):
                for j, x in enumerate(self.x_interpolated):
                    # Find indices in sorted arrays
                    y_idx = np.where(y_interp_sorted == y)[0][0]
                    x_idx = np.where(x_interp_sorted == x)[0][0]
                    spline_grid[i, j] = spline_grid_sorted[y_idx, x_idx]
            
            spline_results = {}
            for i, y in enumerate(self.y_interpolated):
                for j, x in enumerate(self.x_interpolated):
                    spline_results[(x, y)] = spline_grid[i, j]
            self.interpolated_results['spline'] = spline_results
            self.interpolated_z_grid['spline'] = spline_grid
            print("V Spline interpolation complete.")
            
            return True

        except Exception as e:
            print(f"X Error during interpolation: {e}")
            return False
    
    def get_z_at_position(self, x, y, method='bilinear'):
        """
        Get interpolated Z value at a specific X,Y position using a specified method.
        """
        if method not in self.interpolated_z_grid or self.interpolated_z_grid[method] is None:
            print(f"! {method.capitalize()} interpolation grid not created yet.")
            return None

        if (x < self.x_interpolated[0] or x > self.x_interpolated[-1] or
            y < self.y_interpolated[0] or y > self.y_interpolated[-1]):
            print(f"! Position ({x:.3f}, {y:.3f}) is outside interpolation bounds")
            return None

        try:
            # Find the indices corresponding to the coordinates
            x_idx = np.argmin(np.abs(self.x_interpolated - x))
            y_idx = np.argmin(np.abs(self.y_interpolated - y))
            
            z_value = self.interpolated_z_grid[method][y_idx, x_idx]
            return float(z_value) if z_value > 0 else None
            
        except Exception as e:
            print(f"X Error getting Z at ({x:.3f}, {y:.3f}) with {method} method: {e}")
            return None
    
    def get_interpolated_grid_points(self):
        """
        Get all 6x6 interpolated grid points
        
        Returns:
            dict: Dictionary with (x,y) keys and z values
        """
        all_points = {}
        for method in self.interpolated_results:
            all_points.update(self.interpolated_results[method])
        return all_points
    
    def log_interpolation_results(self):
        """
        Generate a detailed log of interpolation results for all methods.
        """
        log = "\n" + "="*80 + "\n"
        log += " " * 25 + "Z INTERPOLATION RESULTS\n"
        log += "="*80 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"AF Points (conceptual 3x3) -> Interpolated to {self.x_points_count}x{self.y_points_count} stitching grid\n"
        log += f"Stitching Offset: {self.stitching_offset}\n"
        log += "-"*80 + "\n"

        # Log AF points data
        log += "AF POINTS MAPPING DATA:\n"
        # Display AF points in conceptual 3x3 grid format for logging
        # We need to reconstruct the conceptual 3x3 grid for logging purposes
        # using the sorted unique x_original_af and y_original_af
        if len(self.x_original_af) > 0 and len(self.y_original_af) > 0:
            # Create a conceptual 3x3 grid for display, even if some points are duplicated
            conceptual_x_af = np.array([self.x_original_af[min(k, len(self.x_original_af)-1)] for k in [0, int(len(self.x_original_af)/2), len(self.x_original_af)-1]])
            conceptual_y_af = np.array([self.y_original_af[min(k, len(self.y_original_af)-1)] for k in [0, int(len(self.y_original_af)/2), len(self.y_original_af)-1]])

            for i in range(3):
                row_str = ""
                for j in range(3):
                    x = conceptual_x_af[j]
                    y = conceptual_y_af[i]
                    z = self.mapping_results.get((x, y), 0)
                    grid_num = i * 3 + j + 1
                    row_str += f"  {grid_num}:({x:.3f},{y:.3f})->Z={z:.4f} |"
                log += row_str[:-2] + "\n"
        log += f"\nUnique AF points mapped: {len(self.mapping_results)}\n"

        # Log results for each method
        for method in ['bilinear', 'cubic', 'spline']:
            if self.interpolated_z_grid.get(method) is None:
                continue

            log += "\n" + "-"*80 + "\n"
            log += f"INTERPOLATED GRID - METHOD: {method.upper()}\n"
            
            grid = self.interpolated_z_grid[method]
            for i in range(self.y_points_count):
                row_str = " ".join([f"{z:7.4f}" for z in grid[i, :]])
                log += f"  {row_str}\n"

            log += "\n" + f"STATISTICS ({method.upper()}):\n"
            z_values = grid[np.isfinite(grid) & (grid > 0)]
            if z_values.size > 0:
                log += f"  Min Z: {np.min(z_values):.4f}\n"
                log += f"  Max Z: {np.max(z_values):.4f}\n"
                log += f"  Mean Z: {np.mean(z_values):.4f}\n"
                log += f"  Std Z: {np.std(z_values):.4f}\n"
                log += f"  Z Range: {np.max(z_values) - np.min(z_values):.4f}\n"

        log += "="*80 + "\n"
        return log

    def save_interpolation_data(self, filename=None):
        """
        Save interpolation data to file
        
        Args:
            filename: Optional filename, if None uses timestamp
        """
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"z_interpolation_{timestamp}.txt"
        
        try:
            with open(filename, 'w') as f:
                f.write(self.log_interpolation_results())
            print(f"V Interpolation data saved to: {filename}")
        except Exception as e:
            print(f"X Error saving interpolation data: {e}")
