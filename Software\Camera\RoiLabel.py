from PyQt5.QtWidgets import QLabel
from PyQt5.QtCore import Qt, QPoint, QRect, QSize, pyqtSlot, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QPen, QColor

# Import MoveToController untuk fitur Move To
try:
    import importlib.util
    import os

    # Coba beberapa path yang mungkin
    possible_paths = [
        "Move To.py",
        "Software/Camera/Move To.py",
        os.path.join(os.path.dirname(__file__), "Move To.py")
    ]

    MoveToController = None
    for path in possible_paths:
        try:
            if os.path.exists(path):
                spec = importlib.util.spec_from_file_location("Move_To", path)
                move_to_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(move_to_module)
                MoveToController = move_to_module.MoveToController
                print(f"MoveToController berhasil diimport dari: {path}")
                break
        except Exception as e:
            continue

    if MoveToController is None:
        print("Warning: MoveToController tidak dapat diimport dari semua path yang dicoba")

except Exception as e:
    print(f"Warning: Error saat import MoveToController: {e}")
    MoveToController = None


class ROILabel(QLabel):
    display_changed = pyqtSignal(QPixmap)
    pixel_clicked = pyqtSignal(QPoint)

    MODE_ROI_SELECTION = 0
    MODE_PIXEL_COORDS  = 1

    def __init__(self, parent=None):
        super().__init__(parent)

        self.original_pixmap = QPixmap() # Menyimpan gambar utuh awal (untuk reset)
        self.display_pixmap = QPixmap()  # Gambar yang selalu terupdate (bisa crop/utuh)

        self.roi_rect = QRect()
        self.begin_point = QPoint()
        self.end_point = QPoint()
        self.is_drawing = False

        self.is_cropped = False # Status apakah label ini sedang menampilkan gambar yang di-crop
        # Menyimpan koordinat (x,y,w,h) dari area crop PADA ORIGINAL_PIXMAP.
        # Ini adalah offset yang akan kita tambahkan ke koordinat piksel.
        self.active_crop_offset_on_original = QRect()

        # Menyimpan resolusi aktual dari camera (akan di-set dari preview camera)
        self.actual_resolution = (1920, 1080)  # Default ke 1920x1080

        # Crosshair settings
        self.show_crosshair = True  # Default tampilkan crosshair
        self.crosshair_color = QColor(0, 0, 0)  # Warna hitam
        self.crosshair_size = 20  # Panjang garis crosshair (pixel)
        self.crosshair_thickness = 2  # Ketebalan garis

        self.setAlignment(Qt.AlignCenter)

        self.mode = self.MODE_ROI_SELECTION

        self.last_clicked_point_screen = QPoint()
        self.last_clicked_point_pixmap = QPoint()

        # Inisialisasi MoveToController untuk fitur Move To
        self.move_controller = None
        self.grbl_instance = None  # Akan di-set dari luar
        if MoveToController is not None:
            try:
                self.move_controller = MoveToController(
                    center_pixel_x=960,      # Titik tengah resolusi penuh 1920x1080
                    center_pixel_y=540,      # Titik tengah resolusi penuh 1920x1080
                    center_grbl_x=3.902,      # Posisi Grbl untuk titik tengah X
                    center_grbl_y=48.456,      # Posisi Grbl untuk titik tengah Y
                    pixel_to_mm_ratio=36.571428571428 ,   # 1 pixel = 0.1 mm
                    grbl_instance=None       # Akan di-set kemudian
                )
                print("MoveToController berhasil diinisialisasi")
            except Exception as e:
                print(f"Error inisialisasi MoveToController: {e}")
                self.move_controller = None

    @pyqtSlot(QPixmap)
    def setPixmap(self, pixmap):
        # Jika pixmap yang masuk adalah original_pixmap (setelah reset), atur is_cropped=False.
        if not pixmap.isNull() and not self.original_pixmap.isNull() and pixmap.size() == self.original_pixmap.size():
            if self.is_cropped:
                # print(f"ROILabel ({self.objectName()}): Membatalkan crop karena menerima original_pixmap via setPixmap.")
                pass
            self.is_cropped = False
            self.active_crop_offset_on_original = QRect() # Reset offset saat menerima original_pixmap
            self.display_pixmap = pixmap
            self.update()

            # Log koordinat crosshair saat pertama kali menerima pixmap
            if not hasattr(self, '_crosshair_logged'):
                crosshair_coords = self.get_crosshair_pixel_coordinates()
                if crosshair_coords:
                    print(f"[INFO] Crosshair center pixel coordinates: ({crosshair_coords.x()}, {crosshair_coords.y()})")
                    print(f"[INFO] Expected center for {self.actual_resolution[0]}x{self.actual_resolution[1]}: ({self.actual_resolution[0]//2}, {self.actual_resolution[1]//2})")
                self._crosshair_logged = True
            return

        # Jika label ini sedang di-crop secara lokal, abaikan stream yang masuk
        if self.is_cropped:
            return

        # Simpan pixmap pertama yang valid sebagai "master copy" untuk di-reset
        if self.original_pixmap.isNull() and not pixmap.isNull():
            self.original_pixmap = pixmap.copy()
        
        self.display_pixmap = pixmap
        self.update()

    def crop_to_roi(self):
        # Gunakan display_pixmap (frame terbaru) sebagai sumber crop
        source_pixmap = self.display_pixmap if not self.display_pixmap.isNull() else self.original_pixmap
        
        # Hitung koordinat ROI pada source_pixmap
        roi_on_source = self._get_roi_in_display_pixmap_coords(self.roi_rect)
        
        if roi_on_source.isNull() or roi_on_source.isEmpty():
            print("ROI tidak valid untuk di-crop.")
            return
        # 🔹 Simpan offset crop terhadap original_pixmap
        self.active_crop_offset_on_original = self._get_roi_in_original_pixmap_coords(self.roi_rect)
        # Potong dari source_pixmap (frame terbaru)
        cropped = source_pixmap.copy(roi_on_source)
        
        self.display_pixmap = cropped
        self.is_cropped = True
        self.display_changed.emit(cropped)
        self.clear_roi()

        print("\n" + "="*50)
        print(f"ROILabel ({self.objectName()}): Crop Berhasil!")
        print(f"  ROI Layar (Widget): {self.roi_rect.x()},{self.roi_rect.y()} -> {self.roi_rect.x()+self.roi_rect.width()},{self.roi_rect.y()+self.roi_rect.height()} ({self.roi_rect.width()}x{self.roi_rect.height()} px)")
        print(f"  ROI Absolut (Original Pixmap): {self.active_crop_offset_on_original.x()},{self.active_crop_offset_on_original.y()} -> {self.active_crop_offset_on_original.x()+self.active_crop_offset_on_original.width()},{self.active_crop_offset_on_original.y()+self.active_crop_offset_on_original.height()} ({self.active_crop_offset_on_original.width()}x{self.active_crop_offset_on_original.height()} px)")
        print(f"  Gambar Asli (Full): {self.original_pixmap.width()}x{self.original_pixmap.height()} px")
        print(f"  Gambar yang Ditampilkan (Cropped): {self.display_pixmap.width()}x{self.display_pixmap.height()} px")
        print("="*50 + "\n")
        
    def reset_view(self):
        if self.is_cropped:
            self.is_cropped = False
            self.active_crop_offset_on_original = QRect()
            # Jangan set display_pixmap ke original_pixmap lama
            # Biarkan setPixmap() dari stream mengupdate display_pixmap
            self.display_changed.emit(QPixmap())  # Emit pixmap kosong sebagai sinyal reset
        self.clear_roi()  # Clear ROI setelah reset

    def clear_roi(self):
        self.roi_rect = QRect()
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.fillRect(self.rect(), QColor(45, 45, 45))

        if not self.display_pixmap.isNull():
            scaled_pixmap = self.display_pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            x = (self.width() - scaled_pixmap.width()) / 2
            y = (self.height() - scaled_pixmap.height()) / 2
            target_rect = QRect(int(x), int(y), scaled_pixmap.width(), scaled_pixmap.height())

            painter.drawPixmap(target_rect, scaled_pixmap)

            # Gambar crosshair di tengah gambar
            if self.show_crosshair:
                self._draw_crosshair(painter, target_rect)
        
        if self.mode == self.MODE_ROI_SELECTION:
            roi_to_draw = QRect(self.begin_point, self.end_point).normalized() if self.is_drawing else self.roi_rect
            if not roi_to_draw.isNull() and roi_to_draw.isValid():
                pen = QPen(QColor(0, 255, 0, 200) if self.is_drawing else QColor(255, 0, 0, 200), 2)
                pen.setStyle(Qt.DashLine if self.is_drawing else Qt.SolidLine)
                painter.setPen(pen)
                painter.drawRect(roi_to_draw)
            
    def _draw_crosshair(self, painter, image_rect):
        """
        Menggambar crosshair (tanda +) di tengah gambar
        """
        if image_rect.isNull() or image_rect.isEmpty():
            return

        # Hitung titik tengah gambar
        center_x = image_rect.x() + image_rect.width() // 2
        center_y = image_rect.y() + image_rect.height() // 2

        # Setup pen untuk crosshair
        pen = QPen(self.crosshair_color, self.crosshair_thickness)
        pen.setStyle(Qt.SolidLine)
        painter.setPen(pen)

        # Gambar garis horizontal
        painter.drawLine(
            center_x - self.crosshair_size // 2, center_y,
            center_x + self.crosshair_size // 2, center_y
        )

        # Gambar garis vertikal
        painter.drawLine(
            center_x, center_y - self.crosshair_size // 2,
            center_x, center_y + self.crosshair_size // 2
        )

    def set_crosshair_visible(self, visible):
        """
        Mengatur visibilitas crosshair
        """
        self.show_crosshair = visible
        self.update()

    def set_crosshair_color(self, color):
        """
        Mengatur warna crosshair
        """
        self.crosshair_color = color
        self.update()

    def set_crosshair_size(self, size):
        """
        Mengatur ukuran crosshair
        """
        self.crosshair_size = size
        self.update()

    def toggle_crosshair(self):
        """
        Toggle on/off crosshair
        """
        self.show_crosshair = not self.show_crosshair
        self.update()
        return self.show_crosshair

    def get_crosshair_pixel_coordinates(self):
        """
        Mendapatkan koordinat pixel dari titik tengah crosshair
        Returns: QPoint dengan koordinat pixel absolut, atau None jika tidak ada gambar
        """
        if self.display_pixmap.isNull():
            return None

        # Dapatkan geometri area gambar yang ditampilkan
        displayed_geometry = self._get_displayed_pixmap_geometry()
        if displayed_geometry.isNull() or displayed_geometry.isEmpty():
            return None

        # Hitung titik tengah dalam koordinat layar
        center_screen_x = displayed_geometry.x() + displayed_geometry.width() // 2
        center_screen_y = displayed_geometry.y() + displayed_geometry.height() // 2
        center_screen_point = QPoint(center_screen_x, center_screen_y)

        # Konversi ke koordinat pixel absolut
        return self._get_pixel_coords_from_screen_point(center_screen_point)

    # Metode helper untuk mendapatkan geometri pixmap yang sedang ditampilkan di widget
    def _get_displayed_pixmap_geometry(self):
        if self.display_pixmap.isNull(): return QRect()
        scaled_size = self.display_pixmap.size().scaled(self.size(), Qt.KeepAspectRatio)
        x = (self.width() - scaled_size.width()) / 2
        y = (self.height() - scaled_size.height()) / 2
        return QRect(int(x), int(y), scaled_size.width(), scaled_size.height())

    # Metode helper baru untuk mendapatkan geometri original_pixmap saat ditampilkan di widget
    def _get_displayed_pixmap_geometry_for_original(self):
        """
        Menghitung geometri tampilan berdasarkan resolusi aktual dari camera
        """
        # Gunakan resolusi aktual dari camera
        from PyQt5.QtCore import QSize
        actual_width, actual_height = self.actual_resolution
        original_size = QSize(actual_width, actual_height)

        scaled_size = original_size.scaled(self.size(), Qt.KeepAspectRatio)
        x = (self.width() - scaled_size.width()) / 2
        y = (self.height() - scaled_size.height()) / 2
        return QRect(int(x), int(y), scaled_size.width(), scaled_size.height())

    # Metode untuk mendapatkan ROI dari layar ke koordinat ORIGINAL_PIXMAP
    def _get_roi_in_original_pixmap_coords(self, roi_screen_rect: QRect):
        """
        Konversi ROI dari koordinat layar ke koordinat pixel absolut pada gambar asli
        Menggunakan resolusi aktual dari camera untuk perhitungan yang akurat
        """
        if not roi_screen_rect.isValid():
            return QRect()

        # Gunakan resolusi aktual dari camera
        source_width, source_height = self.actual_resolution

        # Dapatkan geometri area gambar yang ditampilkan di layar
        displayed_geometry = self._get_displayed_pixmap_geometry()

        if displayed_geometry.width() == 0 or displayed_geometry.height() == 0:
            return QRect()

        # Hitung rasio scaling dari display ke resolusi asli
        x_ratio = source_width / displayed_geometry.width()
        y_ratio = source_height / displayed_geometry.height()

        # Konversi ROI ke koordinat relatif terhadap area gambar yang ditampilkan
        relative_roi = roi_screen_rect.translated(-displayed_geometry.topLeft())

        # Konversi ke koordinat pixel absolut
        px1 = max(0, int(relative_roi.x() * x_ratio))
        py1 = max(0, int(relative_roi.y() * y_ratio))
        px2 = int((relative_roi.x() + relative_roi.width()) * x_ratio)
        py2 = int((relative_roi.y() + relative_roi.height()) * y_ratio)

        # Clamp dalam batas resolusi asli
        px2 = min(px2, source_width)
        py2 = min(py2, source_height)

        print(f"[DEBUG ROI] Screen ROI: {roi_screen_rect}")
        print(f"[DEBUG ROI] Display geometry: {displayed_geometry}")
        print(f"[DEBUG ROI] Source resolution: {source_width}x{source_height}")
        print(f"[DEBUG ROI] Scaling ratio: {x_ratio:.3f}x{y_ratio:.3f}")
        print(f"[DEBUG ROI] Pixel ROI: ({px1}, {py1}) to ({px2}, {py2})")

        return QRect(QPoint(px1, py1), QPoint(px2, py2)).normalized()

    # Metode untuk mendapatkan ROI dari layar ke koordinat DISPLAY_PIXMAP
    def _get_roi_in_display_pixmap_coords(self, roi_screen_rect: QRect):
        if not roi_screen_rect.isValid() or self.display_pixmap.isNull(): return QRect()
        
        displayed_geometry = self._get_displayed_pixmap_geometry()
        
        if displayed_geometry.width() == 0 or displayed_geometry.height() == 0: return QRect()
        
        x_ratio = self.display_pixmap.width() / displayed_geometry.width()
        y_ratio = self.display_pixmap.height() / displayed_geometry.height()
        
        relative_roi = roi_screen_rect.translated(-displayed_geometry.topLeft())
        
        px1 = max(0, int(relative_roi.x() * x_ratio))
        py1 = max(0, int(relative_roi.y() * y_ratio))
        px2 = int((relative_roi.x() + relative_roi.width()) * x_ratio)
        py2 = int((relative_roi.y() + relative_roi.height()) * y_ratio)
        
        px2 = min(px2, self.display_pixmap.width())
        py2 = min(py2, self.display_pixmap.height())

        return QRect(QPoint(px1, py1), QPoint(px2, py2)).normalized()

    def get_roi_size(self):
        if self.roi_rect.isNull() or not self.roi_rect.isValid():
            return None

        screen_size = {
            'width': self.roi_rect.width(),
            'height': self.roi_rect.height()
        }

        # Dapatkan ROI size pada original_pixmap (absolut)
        pixmap_roi_rect = self._get_roi_in_original_pixmap_coords(self.roi_rect)
        pixmap_size = None
        if pixmap_roi_rect:
            pixmap_size = {
                'width': pixmap_roi_rect.width(),
                'height': pixmap_roi_rect.height()
            }
            
        return {
            'screen_size': screen_size,
            'pixmap_size': pixmap_size
        }
    
    def _get_pixel_coords_from_screen_point(self, screen_point: QPoint):
        """
        Konversi titik klik layar ke koordinat pixel absolut pada gambar asli
        Menggunakan resolusi aktual dari camera untuk perhitungan yang akurat
        """
        # Pastikan klik berada dalam area gambar yang ditampilkan
        displayed_geometry = self._get_displayed_pixmap_geometry()
        if not displayed_geometry.contains(screen_point):
            print("ROILabel: Klik di luar area gambar yang ditampilkan.")
            return None

        # Hitung koordinat relatif terhadap area gambar yang ditampilkan
        relative_x = screen_point.x() - displayed_geometry.x()
        relative_y = screen_point.y() - displayed_geometry.y()

        # Gunakan resolusi aktual dari camera
        source_width, source_height = self.actual_resolution

        # Hitung rasio scaling dari display ke resolusi asli
        x_ratio = source_width / displayed_geometry.width()
        y_ratio = source_height / displayed_geometry.height()

        # Konversi ke koordinat pixel absolut
        absolute_x = int(relative_x * x_ratio)
        absolute_y = int(relative_y * y_ratio)

        # Clamp koordinat dalam batas resolusi
        absolute_x = max(0, min(absolute_x, source_width - 1))
        absolute_y = max(0, min(absolute_y, source_height - 1))

        # Debug logging
        print(f"[DEBUG PIXEL] Screen point: {screen_point}")
        print(f"[DEBUG PIXEL] Display geometry: {displayed_geometry}")
        print(f"[DEBUG PIXEL] Relative point: ({relative_x}, {relative_y})")
        print(f"[DEBUG PIXEL] Source resolution: {source_width}x{source_height}")
        print(f"[DEBUG PIXEL] Scaling ratio: {x_ratio:.3f}x{y_ratio:.3f}")
        print(f"[DEBUG PIXEL] Absolute pixel: ({absolute_x}, {absolute_y})")

        # Jika tampilan saat ini di-crop, tambahkan offset dari active_crop_offset_on_original
        if self.is_cropped and not self.active_crop_offset_on_original.isNull():
            # Koordinat sudah relatif terhadap cropped area, tambahkan offset
            final_x = self.active_crop_offset_on_original.x() + absolute_x
            final_y = self.active_crop_offset_on_original.y() + absolute_y

            # Clamp dalam batas original image
            final_x = max(0, min(final_x, source_width - 1))
            final_y = max(0, min(final_y, source_height - 1))

            print(f"[DEBUG PIXEL] Cropped mode - Final pixel: ({final_x}, {final_y})")
            return QPoint(final_x, final_y)
        else:
            return QPoint(absolute_x, absolute_y)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if not self._get_displayed_pixmap_geometry().contains(event.pos()):
                return

            if self.mode == self.MODE_ROI_SELECTION:
                print("ROI mode")
                self.is_drawing = True
                self.begin_point = event.pos()
                self.end_point = event.pos()
                self.update()
            elif self.mode == self.MODE_PIXEL_COORDS:
                print("pixel mode")
                pixel_coords = self._get_pixel_coords_from_screen_point(event.pos())
                if pixel_coords:
                    self.last_clicked_point_screen = event.pos()
                    self.last_clicked_point_pixmap = pixel_coords
                    self.pixel_clicked.emit(pixel_coords)
                    self.update()
                    print(f"Koordinat layar (relatif): {event.pos()}")
                    print(f"Koordinat piksel (absolut): {pixel_coords}")

                    # Integrasi Move To - konversi pixel ke Grbl dan kirim command
                    self._execute_move_to(pixel_coords.x(), pixel_coords.y())
                else:
                    print("pixel mode: Gagal mendapatkan koordinat piksel.")

    def mouseMoveEvent(self, event):
        if self.mode == self.MODE_ROI_SELECTION and self.is_drawing:
            self.end_point = event.pos()
            self.update()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.mode == self.MODE_ROI_SELECTION and self.is_drawing:
                self.is_drawing = False
                self.roi_rect = QRect(self.begin_point, self.end_point).normalized()
                if self.roi_rect.width() < 5 or self.roi_rect.height() < 5:
                    self.roi_rect = QRect()
                self.update()

                # --- TAMBAHAN LOG UNTUK ROI ---
                if not self.roi_rect.isNull() and self.move_controller:
                    # Konversi koordinat layar ROI ke koordinat piksel absolut
                    roi_start_pixel = self._get_pixel_coords_from_screen_point(self.roi_rect.topLeft())
                    roi_end_pixel = self._get_pixel_coords_from_screen_point(self.roi_rect.bottomRight())

                    if roi_start_pixel and roi_end_pixel:
                        # Konversi koordinat piksel ke GRBL
                        grbl_start = self.move_controller.pixel_to_grbl_coordinates(roi_start_pixel.x(), roi_start_pixel.y())
                        grbl_end = self.move_controller.pixel_to_grbl_coordinates(roi_end_pixel.x(), roi_end_pixel.y())

                        print("\n" + "="*60)
                        print(" " * 20 + "ROI SELECTION LOG")
                        print("="*60)
                        print(f"  Area (Pixel Absolut):")
                        print(f"    - Titik Awal (Top-Left):   ({roi_start_pixel.x()}, {roi_start_pixel.y()}) px")
                        print(f"    - Titik Akhir (Bottom-Right): ({roi_end_pixel.x()}, {roi_end_pixel.y()}) px")
                        print(f"    - Ukuran (Lebar x Tinggi): {roi_end_pixel.x() - roi_start_pixel.x()} x {roi_end_pixel.y() - roi_start_pixel.y()} px")
                        print(f"  Area (GRBL):")
                        print(f"    - Posisi Awal (Top-Left):   X={grbl_start[0]:.3f}, Y={grbl_start[1]:.3f} mm")
                        print(f"    - Posisi Akhir (Bottom-Right): X={grbl_end[0]:.3f}, Y={grbl_end[1]:.3f} mm")
                        print("="*60 + "\n")

    @pyqtSlot(int)
    def set_mode(self, mode):
        if mode not in [self.MODE_ROI_SELECTION, self.MODE_PIXEL_COORDS]:
            print(f"[WARNING] Mode {mode} tidak valid untuk ROILabel.")
            return

        self.mode = mode
        self.clear_roi()
        self.last_clicked_point_screen = QPoint()
        self.last_clicked_point_pixmap = QPoint()
        self.update()
        print(f"ROILabel mode diubah ke: {'ROI Selection' if self.mode == self.MODE_ROI_SELECTION else 'Pixel Coordinates'}")

    def _execute_move_to(self, pixel_x: int, pixel_y: int):
        """
        Mengeksekusi fitur Move To: konversi pixel ke koordinat Grbl dan kirim command

        Args:
            pixel_x: Koordinat X dalam pixel dari gambar asli (0-1920)
            pixel_y: Koordinat Y dalam pixel dari gambar asli (0-1080)
        """
        if self.move_controller is None:
            print("Move To: MoveToController tidak tersedia")
            return

        try:
            # Konversi pixel ke koordinat Grbl
            grbl_x, grbl_y = self.move_controller.pixel_to_grbl_coordinates(pixel_x, pixel_y)

            # Debug print untuk menampilkan informasi konversi
            print("=" * 50)
            print("MOVE TO DEBUG INFO:")
            print(f"Pixel coordinates (original): ({pixel_x}, {pixel_y})")
            print(f"Center pixel: ({self.move_controller.center_pixel_x}, {self.move_controller.center_pixel_y})")
            print(f"Center Grbl: ({self.move_controller.center_grbl_x}, {self.move_controller.center_grbl_y})")
            print(f"Pixel to mm ratio: {self.move_controller.pixel_to_mm_ratio}")
            print(f"Grbl coordinates: ({grbl_x:.3f}, {grbl_y:.3f})")

            # Buat G-code command
            gcode_command = f"G0 X{grbl_x:.3f} Y{grbl_y:.3f} F1000"
            print(f"G-code command: {gcode_command}")
            print("=" * 50)

            # Pastikan Grbl instance sudah di-set
            if self.move_controller.grbl_instance is None:
                print("Move To: Grbl instance belum di-set. Command tidak dikirim.")
                return

            # Kirim command ke Grbl melalui instance yang sudah ada
            success = self.move_controller.move_to_pixel(pixel_x, pixel_y)

            if success:
                print(f"Move To: Berhasil bergerak ke pixel ({pixel_x}, {pixel_y}) -> Grbl ({grbl_x:.3f}, {grbl_y:.3f})")
            else:
                print(f"Move To: Gagal bergerak ke pixel ({pixel_x}, {pixel_y})")

        except Exception as e:
            print(f"Move To Error: {e}")

    def set_grbl_instance(self, grbl_instance):
        """
        Set instance Grbl yang akan digunakan untuk Move To

        Args:
            grbl_instance: Instance dari class Grbl yang sudah ada di sistem
        """
        self.grbl_instance = grbl_instance
        if self.move_controller is not None:
            self.move_controller.set_grbl_instance(grbl_instance)
            print("ROILabel: Grbl instance berhasil di-set untuk MoveToController")

    def get_move_controller(self):
        """
        Mendapatkan instance MoveToController

        Returns:
            MoveToController instance atau None
        """
        return self.move_controller
