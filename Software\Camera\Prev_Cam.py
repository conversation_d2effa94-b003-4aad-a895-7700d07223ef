# File: preview.py

import cv2, os
from PyQt5.QtCore import QObject, QTimer, pyqtSlot, Qt
from PyQt5.QtGui import QImage, QPixmap
import numpy as np

# Default FFC file path
ffc_prev = "ffc_prev.npz"

class USBPreview(QObject):
    """
    USB camera preview driver that updates a QLabel with live frames.
    """
    def __init__(self, main_label, preview_label, cam_index=0, ffc_path=None, parent=None):
        super().__init__(parent)
        self.main_label = main_label
        self.preview_label = preview_label
        self.is_swapped = False
        self.extra_label = None
        self.flat_frame = None
        self.dark_frame = None
        self.ffc_enabled = False

        self.is_overridden = False
        self.override_pixmap = None
        self.override_target_label = None  # Label mana yang harus menampilkan override

        # Gunakan default path jika tidak diberikan
        if ffc_path is None:
            ffc_path = os.path.join(os.path.dirname(__file__), ffc_prev)

        # OpenCV capture
        self.cap = cv2.VideoCapture(cam_index, cv2.CAP_MSMF)
        if not self.cap.isOpened():
            raise RuntimeError(f"Cannot open camera index {cam_index}")
        
        # Set resolusi ke 1920x1080
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)

        # Verifikasi resolusi yang sebenarnya diterima
        actual_width = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)

        # Simpan resolusi aktual untuk referensi
        self.actual_width = int(actual_width)
        self.actual_height = int(actual_height)

        print("="*50)
        print("[INFO] VERIFIKASI RESOLUSI PREVIEW CAMERA:")
        print(f"  -> Diminta: 1920 x 1080")
        print(f"  -> Diberikan oleh Kamera: {self.actual_width} x {self.actual_height}")

        if self.actual_width != 1920 or self.actual_height != 1080:
            print(f"  -> WARNING: Resolusi tidak sesuai! ROI calculation mungkin tidak akurat.")
            print(f"  -> Akan menggunakan resolusi aktual untuk perhitungan koordinat.")
        else:
            print(f"  -> OK: Resolusi sesuai dengan yang diminta.")
        print("="*50)
        
        # Load FFC jika file tersedia
        if os.path.exists(ffc_path):
            try:
                data = np.load(ffc_path)
                self.flat_frame = data['flat']
                self.dark_frame = data['dark']
                self.ffc_enabled = True
                print(f"[INFO] FFC loaded from {ffc_path}")
            except Exception as e:
                print(f"[WARNING] Failed to load FFC: {e}")
        else:
            print(f"[INFO] No FFC file found at {ffc_path}, continuing without correction.")

        # Timer to grab frames
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_frame)
        self.start()
    
    def start(self):
        """Mulai timer untuk mengambil frame."""
        # Tentukan interval lagi atau simpan sebagai atribut self
        interval = int(1000 / 30) # Asumsi fps 30
        if not self.timer.isActive():
            self.timer.start(interval)
            print("sudah aktif")
        else:
            print("tidak aktif")

    def _update_frame(self):
        # Ambil frame normal dari kamera preview
        ret, frame = self.cap.read()
        if not ret:
            return

        self.current_frame = frame.copy()

        # Convert BGR to RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Terapkan FFC jika diaktifkan
        if self.ffc_enabled and self.flat_frame is not None and self.dark_frame is not None:
            try:
                frame = (frame.astype(float) - self.dark_frame) / (self.flat_frame - self.dark_frame + 1e-6)
                frame = np.clip(frame * 255, 0, 255).astype(np.uint8)
            except Exception as e:
                print(f"[WARNING] FFC processing failed: {e}")

        h, w, ch = frame.shape
        bytes_per_line = ch * w
        qimg = QImage(frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
        normal_pixmap = QPixmap.fromImage(qimg)

        if normal_pixmap and not normal_pixmap.isNull():
            # Simpan original pixmap untuk ROI calculation yang akurat
            original_pixmap = normal_pixmap.copy()

            # Verifikasi ukuran frame hanya sekali saat startup
            if not hasattr(self, '_frame_size_logged'):
                print(f"[INFO] Frame pixmap size: {original_pixmap.width()} x {original_pixmap.height()}")
                print(f"[INFO] Camera resolution: {self.actual_width} x {self.actual_height}")
                if original_pixmap.width() == self.actual_width and original_pixmap.height() == self.actual_height:
                    print(f"[INFO] Frame size matches camera resolution - ROI calculation will be accurate")
                else:
                    print(f"[WARNING] Frame size mismatch - ROI calculation may be inaccurate")
                self._frame_size_logged = True

            # Logika baru: crop hanya menempel di label yang melakukan crop
            if self.is_swapped:
                # Swap mode: preview camera → main label (normal), crop tetap di preview label
                if self.main_label and self.main_label.isVisible():
                    # Main label selalu mendapat stream normal dari preview camera
                    if hasattr(self.main_label, 'original_pixmap'):
                        self.main_label.original_pixmap = original_pixmap
                        if hasattr(self.main_label, 'actual_resolution'):
                            self.main_label.actual_resolution = (self.actual_width, self.actual_height)

                    scaled_pixmap = normal_pixmap.scaled(
                        self.main_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    self.main_label.setPixmap(scaled_pixmap)

                # Preview label mendapat crop jika ada dan target adalah preview label
                if self.preview_label and self.preview_label.isVisible():
                    if self.is_overridden and self.override_target_label == self.preview_label:
                        # Tampilkan crop
                        scaled_override = self.override_pixmap.scaled(
                            self.preview_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                        )
                        self.preview_label.setPixmap(scaled_override)
                    # Jika tidak ada crop untuk preview label, biarkan kosong atau tampilkan placeholder
            else:
                # Normal mode: preview camera → preview label
                if self.preview_label and self.preview_label.isVisible():
                    # Tentukan pixmap yang akan ditampilkan di preview label
                    if self.is_overridden and self.override_target_label == self.preview_label:
                        # Tampilkan crop
                        scaled_override = self.override_pixmap.scaled(
                            self.preview_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                        )
                        self.preview_label.setPixmap(scaled_override)
                    else:
                        # Tampilkan stream normal
                        if hasattr(self.preview_label, 'original_pixmap'):
                            self.preview_label.original_pixmap = original_pixmap
                            if hasattr(self.preview_label, 'actual_resolution'):
                                self.preview_label.actual_resolution = (self.actual_width, self.actual_height)

                        scaled_pixmap = normal_pixmap.scaled(
                            self.preview_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                        )
                        self.preview_label.setPixmap(scaled_pixmap)

    def stop(self):
        """Stop preview and release camera."""
        self.timer.stop()

    def set_extra_label(self, label):
        self.extra_label = label
    
    def set_override_image(self, pixmap, target_label=None):
        """Mengaktifkan mode override dan menetapkan gambar pengganti untuk label tertentu."""
        self.override_pixmap = pixmap
        self.override_target_label = target_label if target_label else self.preview_label
        self.is_overridden = True
        print(f"[PREV_CAM] Override set for label: {self.override_target_label.objectName() if hasattr(self.override_target_label, 'objectName') else 'unknown'}")

    @pyqtSlot()
    def clear_override(self):
        """Menonaktifkan mode override dan kembali ke streaming normal."""
        self.is_overridden = False
        self.override_pixmap = None
        self.override_target_label = None
        print("[PREV_CAM] Override cleared")

    def swap_streams(self):
        self.is_swapped = not self.is_swapped
